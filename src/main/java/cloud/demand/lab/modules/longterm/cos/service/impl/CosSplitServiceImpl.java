package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleByAlgorithmDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutSplitVersionDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.service.CosSplitService;
import cloud.demand.lab.modules.longterm.cos.vo.CosLongtermPredictOutPurchaseSplitVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class CosSplitServiceImpl implements CosSplitService {

    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private DBHelper planCosDBHelper;

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public Long split(Long taskId) {
        // 1. 查询任务信息并创建拆分版本
        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new BizException("预测任务不存在，taskId: " + taskId);
        }
        log.info("开始拆分任务，taskId: {}, categoryName: {}", taskId, taskDO.getCategoryName());
        
        // 创建拆分版本
        CosLongtermPredictOutSplitVersionDO splitVersionDO = createSplitVersion(taskDO);
        Long splitVersionId = splitVersionDO.getId();
        
        log.info("创建拆分版本成功，splitVersionId: {}", splitVersionId);

        // 2. 计算从当前月份开始到当前半年结束、以及后续每半年的增量
        List<CosLongtermPredictOutPurchaseSplitVO> purchaseSplitList = calculatePurchaseSplit(taskId);

        // 3. 按Prophet算法的月份占比拆分月份的增量
        List<CosLongtermPredictOutPurchaseSplitVO> monthlySplitList = splitByProphetMonthlyRatio(taskId, purchaseSplitList);

        // 4. 按照最近1年的地域占比拆分


        // 5. 写入拆分结果表中

        return splitVersionId;
    }

    private CosLongtermPredictOutSplitVersionDO createSplitVersion(CosLongtermPredictTaskDO taskDO) {
        CosLongtermPredictOutSplitVersionDO splitVersionDO = new CosLongtermPredictOutSplitVersionDO();
        splitVersionDO.setTaskId(taskDO.getId());
        splitVersionDO.setName("默认拆分版本_" + LocalDateTime.now().toString().substring(0, 19));
        splitVersionDO.setNote("");
        splitVersionDO.setCreator(LoginUtils.getUserName());

        cdLabDbHelper.insert(splitVersionDO);
        return splitVersionDO;
    }

    /**
     * 计算从当前月份开始到当前半年结束、以及后续每半年的增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> calculatePurchaseSplit(Long taskId) {
        // 1）找到cos_longterm_predict_input_scale中当前任务的最新的date，月末日期
        LocalDate inputDateLatest = getInputDateLatest(taskId);

        // 2）通过cos_longterm_predict_input_scale找到inputDateLatest这一天的存量规模
        Map<String, BigDecimal> baseScaleMap = getBaseScale(taskId, inputDateLatest);

        // 3）对于cos_longterm_predict_out_scale中当前任务的预测结果，按strategy_type分开进行
        return calculateIncrements(taskId, inputDateLatest, baseScaleMap);
    }

    /**
     * 找到cos_longterm_predict_input_scale中当前任务的最新的date
     * 如果date是每月最后一天，那么就取这一天；否则取这个天的上一个月的最后一天
     */
    private LocalDate getInputDateLatest(Long taskId) {
        // 查询当前任务的最新日期
        LocalDate latestDate = cdLabDbHelper.getRawOne(LocalDate.class,
            "SELECT MAX(date) FROM cos_longterm_predict_input_scale WHERE task_id = ?", taskId);
        if (latestDate == null) {
            throw new BizException("未找到任务的输入数据，taskId: " + taskId);
        }

        LocalDate lastDayOfMonth = latestDate.with(TemporalAdjusters.lastDayOfMonth());
        if (latestDate.equals(lastDayOfMonth)) {
            return latestDate;
        } else {
            return latestDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        }
    }

    /**
     * 获取inputDateLatest这一天的is_out_customer=1和=0的cur_scale存量规模
     */
    private Map<String, BigDecimal> getBaseScale(Long taskId, LocalDate inputDateLatest) {
        List<CosLongtermPredictInputScaleDO> inputScaleList = cdLabDbHelper.getAll(
            CosLongtermPredictInputScaleDO.class,
            "WHERE task_id = ? AND date = ? AND is_out_customer IN (0, 1)", taskId, inputDateLatest);
        if (ListUtils.isEmpty(inputScaleList)) {
            throw new BizException("未找到基准日期的存量数据，taskId: " + taskId + ", date: " + inputDateLatest);
        }

        // 按is_out_customer分组，构建key为"is_out_customer"的Map
        Map<String, BigDecimal> baseScaleMap = new HashMap<>();
        for (CosLongtermPredictInputScaleDO inputScale : inputScaleList) {
            String key = String.valueOf(inputScale.getIsOutCustomer());
            baseScaleMap.put(key, inputScale.getCurScale());
        }

        return baseScaleMap;
    }

    /**
     * 计算预测结果的净增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> calculateIncrements(Long taskId, LocalDate inputDateLatest,
                                                                          Map<String, BigDecimal> baseScaleMap) {
        List<CosLongtermPredictOutPurchaseSplitVO> result = new ArrayList<>();

        // 查询预测结果数据
        List<CosLongtermPredictOutScaleDO> predictScaleList = cdLabDbHelper.getAll(
            CosLongtermPredictOutScaleDO.class, "where task_id = ?", taskId);
        if (ListUtils.isEmpty(predictScaleList)) {
            log.warn("未找到预测结果数据，taskId: {}", taskId);
            return result;
        }

        // 按strategy_type和is_out_customer分组
        Map<String, List<CosLongtermPredictOutScaleDO>> groupedData = new HashMap<>();
        for (CosLongtermPredictOutScaleDO predictScale : predictScaleList) {
            String groupKey = predictScale.getStrategyType() + "_" + predictScale.getIsOutCustomer();
            groupedData.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(predictScale);
        }

        // 对每个组合进行处理
        for (Map.Entry<String, List<CosLongtermPredictOutScaleDO>> entry : groupedData.entrySet()) {
            String groupKey = entry.getKey();
            List<CosLongtermPredictOutScaleDO> groupData = entry.getValue();

            String[] keyParts = groupKey.split("_");
            String strategyType = keyParts[0];
            Boolean isOutCustomer = Boolean.valueOf(keyParts[1]);

            // 计算该组合的增量
            List<CosLongtermPredictOutPurchaseSplitVO> groupResult = calculateGroupIncrements(
                taskId, strategyType, isOutCustomer, groupData, inputDateLatest, baseScaleMap);
            result.addAll(groupResult);
        }

        return result;
    }

    /**
     * 计算单个strategy_type和is_out_customer组合的增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> calculateGroupIncrements(Long taskId, String strategyType,
                                                                               Boolean isOutCustomer,
                                                                               List<CosLongtermPredictOutScaleDO> groupData,
                                                                               LocalDate inputDateLatest,
                                                                               Map<String, BigDecimal> baseScaleMap) {
        List<CosLongtermPredictOutPurchaseSplitVO> result = new ArrayList<>();

        // 按日期排序
        ListUtils.sortAscNullLast(groupData, CosLongtermPredictOutScaleDO::getDate);

        // 获取基准存量
        String baseKey = String.valueOf(isOutCustomer ? 1 : 0);
        BigDecimal previousScale = baseScaleMap.getOrDefault(baseKey, BigDecimal.ZERO);
        LocalDate previousDate = inputDateLatest;

        // 逐个计算净增量
        for (CosLongtermPredictOutScaleDO predictScale : groupData) {
            LocalDate currentDate = predictScale.getDate();
            BigDecimal currentScale = predictScale.getPredictScale();

            // 计算净增量 = 当前预测存量 - 上一个日期的存量
            BigDecimal increment = currentScale.subtract(previousScale);

            // 构造CosLongtermPredictOutPurchaseSplitVO
            CosLongtermPredictOutPurchaseSplitVO splitVO = createPurchaseSplitVO(
                taskId, strategyType, isOutCustomer, previousDate, currentDate, increment);

            // 设置过程信息
            splitVO.setPreviousScale(previousScale);
            splitVO.setCurrentScale(currentScale);

            result.add(splitVO);

            // 更新为下一次计算的基准
            previousScale = currentScale;
            previousDate = currentDate;
        }

        return result;
    }

    /**
     * 创建CosLongtermPredictOutPurchaseSplitVO对象
     */
    private CosLongtermPredictOutPurchaseSplitVO createPurchaseSplitVO(Long taskId, String strategyType,
                                                                      Boolean isOutCustomer, LocalDate startDate,
                                                                      LocalDate endDate, BigDecimal increment) {
        CosLongtermPredictOutPurchaseSplitVO splitVO = new CosLongtermPredictOutPurchaseSplitVO();

        // 设置基本信息
        splitVO.setTaskId(taskId);
        splitVO.setStrategyType(strategyType);
        splitVO.setIsOutCustomer(isOutCustomer);
        splitVO.setDate(endDate); // 以结束日期作为记录日期
        splitVO.setPurchaseStorage(increment); // 净增量作为采购存储量

        // splitDO的4个日期关联字段暂不设置

        // 设置过程信息字段
        splitVO.setStartDate(startDate);
        splitVO.setEndDate(endDate);

        // 设置过程信息到splitLog中
        String splitLog = String.format("计算净增量[计算期间:%s至%s, 净增量:%sPB, 策略类型:%s, 外部客户:%s] ",
                startDate, endDate, increment, strategyType, isOutCustomer);
        splitVO.setSplitLog(splitLog);

        return splitVO;
    }

    /**
     * 按Prophet算法的月份占比拆分月份的增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> splitByProphetMonthlyRatio(Long taskId,
                                                                                 List<CosLongtermPredictOutPurchaseSplitVO> purchaseSplitList) {
        if (ListUtils.isEmpty(purchaseSplitList)) {
            return new ArrayList<>();
        }

        // 1）先找出purchaseSplitList的最小startDate和最大endDate，确定时间范围，找到这个时间范围的所有月末日期
        List<LocalDate> allMonthEndDates = getAllMonthEndDates(purchaseSplitList);

        // 2）查找cos_longterm_predict_out_scale_by_algorithm表中当前taskId且algorithm='Prophet'，date属于allMonthEndDates集合的所有预测值
        List<CosLongtermPredictOutScaleByAlgorithmDO> prophetScalePredicts = getProphetScalePredicts(taskId, allMonthEndDates);

        // 3）对于purchaseSplitList中的每一个元素，按prophet预测结果的月份净增比重来拆分
        List<CosLongtermPredictOutPurchaseSplitVO> result = new ArrayList<>();
        for (CosLongtermPredictOutPurchaseSplitVO purchaseSplit : purchaseSplitList) {
            List<CosLongtermPredictOutPurchaseSplitVO> splitResult = splitSinglePurchaseByProphet(
                purchaseSplit, prophetScalePredicts);
            result.addAll(splitResult);
        }

        return result;
    }

    /**
     * 找出purchaseSplitList的最小startDate和最大endDate，确定时间范围，找到这个时间范围的所有月末日期
     */
    private List<LocalDate> getAllMonthEndDates(List<CosLongtermPredictOutPurchaseSplitVO> purchaseSplitList) {
        // 找出最小startDate和最大endDate
        LocalDate minStartDate = NumberUtils.min(purchaseSplitList, o -> o.getStartDate());
        LocalDate maxEndDate = NumberUtils.max(purchaseSplitList, o -> o.getEndDate());

        // 生成这个时间范围内的所有月末日期
        List<LocalDate> monthEndDates = new ArrayList<>();
        LocalDate current = minStartDate.with(TemporalAdjusters.lastDayOfMonth());

        while (!current.isAfter(maxEndDate)) {
            monthEndDates.add(current);
            current = current.plusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        }

        return monthEndDates;
    }

    /**
     * 查找cos_longterm_predict_out_scale_by_algorithm表中当前taskId且algorithm='Prophet'，date属于allMonthEndDates集合的所有预测值
     */
    private List<CosLongtermPredictOutScaleByAlgorithmDO> getProphetScalePredicts(Long taskId, List<LocalDate> allMonthEndDates) {
        if (ListUtils.isEmpty(allMonthEndDates)) {
            return new ArrayList<>();
        }

        // 使用WhereSQL构造查询条件，因为条件超过2个
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_id = ?", taskId);
        whereSQL.and("algorithm = ?", "Prophet");
        whereSQL.and("date in (?)", allMonthEndDates);
        whereSQL.and("is_out_customer in (0,1)");

        return cdLabDbHelper.getAll(CosLongtermPredictOutScaleByAlgorithmDO.class,
            whereSQL.getSQL(), whereSQL.getParams());
    }

    /**
     * 对单个CosLongtermPredictOutPurchaseSplitVO按prophet预测结果的月份净增比重来拆分
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> splitSinglePurchaseByProphet(
            CosLongtermPredictOutPurchaseSplitVO purchaseSplit,
            List<CosLongtermPredictOutScaleByAlgorithmDO> prophetScalePredicts) {

        List<CosLongtermPredictOutPurchaseSplitVO> result = new ArrayList<>();

        // 找到相同isOutCustomer和处于startDate、endDate之间的Prophet预测数据
        Integer isOutCustomerInt = purchaseSplit.getIsOutCustomer() ? 1 : 0;
        List<CosLongtermPredictOutScaleByAlgorithmDO> relevantPredicts = ListUtils.filter(prophetScalePredicts, predict ->
            Objects.equals(predict.getIsOutCustomer(), isOutCustomerInt) &&
            !predict.getDate().isBefore(purchaseSplit.getStartDate()) &&
            !predict.getDate().isAfter(purchaseSplit.getEndDate())
        );

        if (ListUtils.isEmpty(relevantPredicts)) {
            log.warn("未找到相关的Prophet预测数据，purchaseSplit: {}", purchaseSplit);
            return result;
        }

        // 按日期排序
        ListUtils.sortAscNullLast(relevantPredicts, CosLongtermPredictOutScaleByAlgorithmDO::getDate);

        // 计算每个月的净增量和比例
        List<MonthlyIncrement> monthlyIncrements = calculateMonthlyIncrements(purchaseSplit, relevantPredicts);

        // 按净增量比例拆分原始的purchaseStorage
        BigDecimal totalPurchaseStorage = purchaseSplit.getPurchaseStorage();
        for (MonthlyIncrement monthlyIncrement : monthlyIncrements) {
            CosLongtermPredictOutPurchaseSplitVO monthlySplit = createMonthlySplit(
                purchaseSplit, monthlyIncrement, totalPurchaseStorage);
            result.add(monthlySplit);
        }

        return result;
    }

    /**
     * 计算每个月的净增量和比例
     */
    private List<MonthlyIncrement> calculateMonthlyIncrements(CosLongtermPredictOutPurchaseSplitVO purchaseSplit,
                                                             List<CosLongtermPredictOutScaleByAlgorithmDO> relevantPredicts) {
        List<MonthlyIncrement> monthlyIncrements = new ArrayList<>();

        // 获取起始存量，如果startDate不在预测数据中，使用previousScale
        BigDecimal previousScale = purchaseSplit.getPreviousScale();
        LocalDate startDate = purchaseSplit.getStartDate();

        // 检查startDate是否在预测数据中
        List<CosLongtermPredictOutScaleByAlgorithmDO> startDatePredicts = ListUtils.filter(relevantPredicts,
            predict -> predict.getDate().equals(purchaseSplit.getStartDate()));

        if (ListUtils.isNotEmpty(startDatePredicts)) {
            previousScale = startDatePredicts.get(0).getPredictScale();
        }

        // 逐个计算每个月的净增量
        for (CosLongtermPredictOutScaleByAlgorithmDO predict : relevantPredicts) {
            if (startDate.equals(predict.getDate())) {
                continue;
            }
            LocalDate currentDate = predict.getDate();
            BigDecimal currentScale = predict.getPredictScale();

            // 计算净增量
            BigDecimal increment = currentScale.subtract(previousScale);

            MonthlyIncrement monthlyIncrement = new MonthlyIncrement();
            monthlyIncrement.setStartDate(startDate);
            monthlyIncrement.setEndDate(currentDate);
            monthlyIncrement.setIncrement(increment);
            monthlyIncrement.setPreviousScale(previousScale);
            monthlyIncrement.setCurrentScale(currentScale);

            monthlyIncrements.add(monthlyIncrement);

            // 更新为下一次计算的基准
            previousScale = currentScale;
            startDate = currentDate;
        }

        // 计算总净增量和每个月的比例
        BigDecimal totalIncrement = monthlyIncrements.stream()
            .map(MonthlyIncrement::getIncrement)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalIncrement.compareTo(BigDecimal.ZERO) != 0) {
            for (MonthlyIncrement monthlyIncrement : monthlyIncrements) {
                BigDecimal ratio = monthlyIncrement.getIncrement().divide(totalIncrement, 8, RoundingMode.HALF_UP);
                monthlyIncrement.setRatio(ratio);
            }
        } else {
            // 如果总净增量为0，平均分配
            BigDecimal averageRatio = BigDecimal.ONE.divide(BigDecimal.valueOf(monthlyIncrements.size()), 8, RoundingMode.HALF_UP);
            for (MonthlyIncrement monthlyIncrement : monthlyIncrements) {
                monthlyIncrement.setRatio(averageRatio);
            }
        }

        return monthlyIncrements;
    }

    /**
     * 创建月度拆分记录
     */
    private CosLongtermPredictOutPurchaseSplitVO createMonthlySplit(CosLongtermPredictOutPurchaseSplitVO originalSplit,
                                                                   MonthlyIncrement monthlyIncrement,
                                                                   BigDecimal totalPurchaseStorage) {
        CosLongtermPredictOutPurchaseSplitVO monthlySplit = new CosLongtermPredictOutPurchaseSplitVO();

        // 复制原始数据的基本信息
        monthlySplit.setTaskId(originalSplit.getTaskId());
        monthlySplit.setStrategyType(originalSplit.getStrategyType());
        monthlySplit.setIsOutCustomer(originalSplit.getIsOutCustomer());
        monthlySplit.setDate(monthlyIncrement.getEndDate());

        // 按比例分配采购存储量
        BigDecimal monthlyPurchaseStorage = totalPurchaseStorage.multiply(monthlyIncrement.getRatio())
            .setScale(4, RoundingMode.HALF_UP);
        monthlySplit.setPurchaseStorage(monthlyPurchaseStorage);

        // 设置过程信息
        monthlySplit.setStartDate(monthlyIncrement.getStartDate());
        monthlySplit.setEndDate(monthlyIncrement.getEndDate());
        monthlySplit.setPreviousScale(monthlyIncrement.getPreviousScale());
        monthlySplit.setCurrentScale(monthlyIncrement.getCurrentScale());

        // 设置拆分日志
        String splitLog = String.format("Prophet月度拆分[月度日期:%s, 月度净增量:%sPB, 月度比例:%s, 月度采购量:%sPB, 策略类型:%s, 外部客户:%s] ",
                monthlyIncrement.getEndDate(),
                monthlyIncrement.getIncrement(), monthlyIncrement.getRatio(), monthlyPurchaseStorage,
                originalSplit.getStrategyType(), originalSplit.getIsOutCustomer());
        monthlySplit.setSplitLog(originalSplit.getSplitLog() + ";" + splitLog);

        return monthlySplit;
    }

    /**
     * 月度净增量辅助类
     */
    @Data
    private static class MonthlyIncrement {
        private LocalDate startDate;
        private LocalDate endDate;
        private BigDecimal increment;
        private BigDecimal ratio;
        private BigDecimal previousScale;
        private BigDecimal currentScale;

    }
}
