package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * cos中长期剔除大客户后算法模型预测输出量
 */
@Data
@ToString
@Table("cos_longterm_predict_out_scale_by_algorithm")
public class CosLongtermPredictOutScaleByAlgorithmDO extends BaseDO {

    /** 任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 方案id<br/>Column: [category_id] */
    @Column(value = "category_id")
    private Long categoryId;

    /** 算法名称，例如ARIMA、linregress、Prophet<br/>Column: [algorithm] */
    @Column(value = "algorithm")
    private String algorithm;

    /** 日期<br/>Column: [date] */
    @Column(value = "date")
    private LocalDate date;

    /** 是否外部客户，0表示否，1表示是，-1表示全部客户<br/>Column: [is_out_customer] */
    @Column(value = "is_out_customer")
    private Integer isOutCustomer;

    /** 预测出来的当前存量(单位PB)<br/>Column: [predict_scale] */
    @Column(value = "predict_scale")
    private BigDecimal predictScale;

}