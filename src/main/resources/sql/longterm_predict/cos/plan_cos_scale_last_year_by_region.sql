select DATE_FORMAT(STR_TO_DATE(data_date, '%Y%m%d'), '%Y-%m-%d') as date,
       (case when cloud_product_id_1=5 then 0 -- 自研COS一定是内部
             when category_name_2 like '%内部%' then 0
             else 1 end) as is_out_customer,
    region_name,
       sum(amount) as value
from end_to_end_provide.cloud_end_to_end_utilization
where

    ${CONDITION}

  and region_name!=''
  and data_date in (:lastYearEnd, (select max(data_date) from cloud_end_to_end_utilization))
group by data_date,category_name_2,region_name
order by data_date,category_name_2,region_name